import 'package:flutter/material.dart';
import '../providers/app_state.dart'; // To access BreathingTechnique
import '../theme/app_theme.dart'; // For colors

class CustomBreathingAnimation extends StatelessWidget {
  final AnimationController controller;
  final BreathingTechnique technique;
  final String currentPhaseKey;
  final bool isCompleted;

  const CustomBreathingAnimation({
    super.key,
    required this.controller,
    required this.technique,
    required this.currentPhaseKey,
    required this.isCompleted,
  });

  /// Generate phase-specific colors based on the technique's base color
  Color _getPhaseColor(String phase, Color baseColor, double animationValue) {
    if (isCompleted) {
      return Colors.green; // Completion color
    }

    switch (phase) {
      case "get_ready":
        return baseColor.withValues(alpha: 0.7); // Neutral base color
      case "inhale":
        // Brighter/more vibrant shade - increase saturation and lightness
        final hsl = HSLColor.fromColor(baseColor);
        final brightColor = hsl
            .withSaturation((hsl.saturation + 0.2).clamp(0.0, 1.0))
            .withLightness((hsl.lightness + 0.1).clamp(0.0, 1.0))
            .toColor();
        // Smooth transition during inhale
        return Color.lerp(baseColor, brightColor, animationValue) ?? baseColor;
      case "hold1":
      case "hold":
        // Deeper/more intense shade - use muted teal/deep blue
        return const Color(0xFF2F4F4F); // Dark slate gray (calming)
      case "exhale":
        // Softer/more subdued shade returning to base
        final hsl = HSLColor.fromColor(baseColor);
        final softColor = hsl
            .withSaturation((hsl.saturation - 0.1).clamp(0.0, 1.0))
            .withLightness((hsl.lightness + 0.2).clamp(0.0, 1.0))
            .toColor();
        // Smooth transition during exhale
        return Color.lerp(softColor, baseColor, animationValue) ?? baseColor;
      case "hold2":
        // Hold after exhale - return to calm base color
        return baseColor.withValues(alpha: 0.8);
      default:
        return baseColor;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Get screen dimensions for responsive sizing
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    // Calculate appropriate size based on screen dimensions
    // Use the smaller dimension to ensure it fits on all devices
    final smallerDimension =
        screenWidth < screenHeight ? screenWidth : screenHeight;
    final circleSize = smallerDimension * 0.6; // 60% of the smaller dimension

    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        double progress = controller.value; // 0.0 to 1.0

        // Determine visual progress based on phase with improved curves
        if (currentPhaseKey == "get_ready") {
          progress = 0.0; // Start contracted
        } else if (isCompleted) {
          progress = 1.0; // Show completed state (full)
        } else if (currentPhaseKey == "inhale") {
          // Inhale: grow from 0.0 to 1.0 with ease-in curve for natural breathing
          final curve = Curves.easeIn;
          progress = curve.transform(controller.value);
        } else if (currentPhaseKey.contains("hold") ||
            currentPhaseKey == "hold1") {
          // Hold after inhale: stay expanded
          progress = 1.0;
        } else if (currentPhaseKey == "exhale") {
          // Exhale: shrink from 1.0 to 0.0 with ease-out curve for natural breathing
          final curve = Curves.easeOut;
          progress = 1.0 - curve.transform(controller.value);
        } else if (currentPhaseKey == "hold2") {
          // Hold after exhale (used in Box Breathing): stay contracted
          progress = 0.0;
        }

        // Get dynamic phase-based color
        final progressColor =
            _getPhaseColor(currentPhaseKey, technique.color, controller.value);

        // Use LayoutBuilder to ensure the animation respects its container constraints
        return LayoutBuilder(builder: (context, constraints) {
          // Use the smaller of the available width/height or our calculated size
          final availableSize = constraints.maxWidth < constraints.maxHeight
              ? constraints.maxWidth
              : constraints.maxHeight;

          // Ensure the size doesn't exceed the available space
          final size =
              circleSize < availableSize ? circleSize : availableSize * 0.9;

          return SizedBox(
            width: size,
            height: size,
            child: CustomPaint(
              painter: CirclePainter(
                progress: progress,
                color: progressColor,
                isCompleted: isCompleted,
                phaseColor: progressColor,
                animationValue: controller.value,
              ),
            ),
          );
        });
      },
    );
  }
}

// --- Circle Painter --- (Used for all breathing techniques)
class CirclePainter extends CustomPainter {
  final double progress; // 0.0 (contracted) to 1.0 (expanded)
  final Color color;
  final bool isCompleted;
  final Color phaseColor;
  final double animationValue;

  CirclePainter({
    required this.progress,
    required this.color,
    required this.isCompleted,
    required this.phaseColor,
    required this.animationValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final maxRadius = size.width / 2;
    final minRadius =
        maxRadius * 0.5; // Smaller minimum size for better visual effect
    final currentRadius = minRadius + (maxRadius - minRadius) * progress;

    // Background (optional, can be handled by container)
    final backgroundPaint = Paint()
      ..color = AppTheme.primaryColor.withValues(alpha: 0.1)
      ..style = PaintingStyle.fill;
    canvas.drawCircle(center, maxRadius, backgroundPaint);

    // Pulsating Halo Effect - draw before main circle
    if (!isCompleted) {
      final haloRadius =
          maxRadius * (1.1 + 0.1 * animationValue); // Slightly larger than max
      final haloOpacity = (0.3 * (1.0 - animationValue))
          .clamp(0.0, 0.3); // Fade out as animation progresses
      final haloPaint = Paint()
        ..color = phaseColor.withValues(alpha: haloOpacity)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 3.0;
      canvas.drawCircle(center, haloRadius, haloPaint);
    }

    // Animated Circle
    final foregroundPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    canvas.drawCircle(center, currentRadius, foregroundPaint);

    // Checkmark on completion
    if (isCompleted) {
      _drawCheckmark(canvas, center, maxRadius * 0.5);
    }
  }

  @override
  bool shouldRepaint(covariant CirclePainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.color != color ||
        oldDelegate.isCompleted != isCompleted ||
        oldDelegate.phaseColor != phaseColor ||
        oldDelegate.animationValue != animationValue;
  }
}

// Helper function to draw checkmark
void _drawCheckmark(Canvas canvas, Offset center, double size) {
  final paint = Paint()
    ..color = Colors.white
    ..style = PaintingStyle.stroke
    ..strokeWidth = size * 0.15 // Adjust thickness relative to size
    ..strokeCap = StrokeCap.round;

  final path = Path();
  // Define checkmark points relative to center and size
  path.moveTo(center.dx - size * 0.4, center.dy + size * 0.0);
  path.lineTo(center.dx - size * 0.1, center.dy + size * 0.3);
  path.lineTo(center.dx + size * 0.4, center.dy - size * 0.3);

  canvas.drawPath(path, paint);
}
